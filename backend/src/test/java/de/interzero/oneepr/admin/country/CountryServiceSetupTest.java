package de.interzero.oneepr.admin.country;

import de.interzero.oneepr.admin.service_setup.ServiceSetup;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Test to verify that the ServiceSetup embedded object is properly initialized
 * when loading Country entities from the database.
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class CountryServiceSetupTest {

    @Autowired
    private CountryRepository countryRepository;

    @Test
    void testServiceSetupIsNotNullWhenLoadingCountryByCode() {
        // Given: A country exists in the database (should be seeded by migration scripts)
        // We'll use a common country code that should exist
        String countryCode = "DE"; // Germany
        
        // When: We load the country by code
        Country country = countryRepository.findByCode(countryCode)
                .orElseThrow(() -> new AssertionError("Country with code " + countryCode + " should exist"));
        
        // Then: The serviceSetup should not be null
        assertThat(country.getServiceSetup())
                .as("ServiceSetup should not be null after loading Country from database")
                .isNotNull();
        
        // And: The serviceSetup should be a proper instance with initialized collections
        ServiceSetup serviceSetup = country.getServiceSetup();
        assertThat(serviceSetup.getPackagingServices())
                .as("PackagingServices list should be initialized")
                .isNotNull();
        assertThat(serviceSetup.getCriterias())
                .as("Criterias list should be initialized")
                .isNotNull();
        assertThat(serviceSetup.getThirdPartyCosts())
                .as("ThirdPartyCosts list should be initialized")
                .isNotNull();
        assertThat(serviceSetup.getRepresentativeTiers())
                .as("RepresentativeTiers list should be initialized")
                .isNotNull();
        assertThat(serviceSetup.getObligationCheckSections())
                .as("ObligationCheckSections list should be initialized")
                .isNotNull();
        assertThat(serviceSetup.getRequiredInformations())
                .as("RequiredInformations list should be initialized")
                .isNotNull();
        assertThat(serviceSetup.getCountryPriceLists())
                .as("CountryPriceLists list should be initialized")
                .isNotNull();
    }

    @Test
    void testServiceSetupIsNotNullForNewCountryInstance() {
        // Given: A new Country instance
        Country country = new Country();
        
        // Then: The serviceSetup should be initialized by default
        assertThat(country.getServiceSetup())
                .as("ServiceSetup should be initialized by default for new Country instances")
                .isNotNull();
    }
}
